package com.morningstar.martapi.config;

import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.service.JwtVerificationService;
import org.springframework.beans.factory.annotation.Value;
import com.morningstar.martapi.validator.AuthTokenValidator;
import com.morningstar.martapi.validator.ProductIdValidator;
import com.morningstar.martapi.validator.RequestIdValidator;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martapi.validator.delta.DeltaStartTimeValidator;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martapi.validator.investmentapi.ColumnLimitValidator;
import com.morningstar.martapi.validator.investmentapi.DataPointValidator;
import com.morningstar.martapi.validator.investmentapi.DateValidator;
import com.morningstar.martapi.validator.investmentapi.IdTypeValidator;
import com.morningstar.martapi.validator.investmentapi.InvestmentValidator;
import com.morningstar.martapi.validator.investmentapi.DynamicDatapointValidator;
import com.morningstar.martapi.validator.investmentapi.UseCaseValidator;
import com.morningstar.martapi.validator.portfolioholdings.HoldingDateTypeValidator;
import com.morningstar.martapi.validator.portfolioholdings.HoldingsDataPointsRequestFieldsValidator;
import com.morningstar.martapi.validator.portfolioholdings.HoldingsDataPointsCountValidator;
import com.morningstar.martapi.validator.portfolioholdings.InvestmentCountValidator;
import com.morningstar.martapi.validator.portfolioholdings.StaticDateCountValidator;
import com.morningstar.martapi.validator.portfolioholdings.PortfolioSettingsValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestDataPointValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestDateValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestInvestmentValidator;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.entitlement.service.DataEntitlementService;
import com.morningstar.martgateway.infrastructures.config.ProductIdsRegistry;
import com.morningstar.martgateway.interfaces.model.holdingdata.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import com.morningstar.martgateway.util.EquityDatapointUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

@Configuration
public class ValidatorsConfig {

    @Bean
    public JwtVerificationService jwtVerificationService(@Value("${jwt.jwks.url}") String jwksUrl) {
        return new JwtVerificationService(jwksUrl);
    }

    @Bean(name = "investmentApiValidator")
    public RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> investmentApiValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            JwtVerificationService jwtVerificationService
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(jwtVerificationService),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
        List<Validator<InvestmentApiRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new ColumnLimitValidator(1000, new EquityDatapointUtil()),
                new IdTypeValidator(),
                new DeltaStartTimeValidator(),
                new DynamicDatapointValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "deltaDetectionApiValidator")
    public RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> deltaDetectionApiValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            JwtVerificationService jwtVerificationService
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(jwtVerificationService),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
        List<Validator<InvestmentApiRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new DeltaStartTimeValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "asyncApiValidator")
    public RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> asyncDataApiValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            @Qualifier("dataEntitlementService")DataEntitlementService<InvestmentApiRequest> dataEntitlementService,
            JwtVerificationService jwtVerificationService
            ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(jwtVerificationService),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
        List<Validator<InvestmentApiRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new ColumnLimitValidator(new EquityDatapointUtil()),
                new IdTypeValidator(),
                new DeltaStartTimeValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators, dataEntitlementService);
    }

    @Bean(name = "timeSeriesApiValidator")
    public RequestValidationHandler<HeadersAndParams, MartRequest> timeSeriesApiValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            JwtVerificationService jwtVerificationService
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(
                new AuthTokenValidator(jwtVerificationService),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
        List<Validator<MartRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(ValidationException::new),
                new TsMartRequestDataPointValidator(),
                new TsMartRequestInvestmentValidator(),
                new TsMartRequestDateValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "holdingDateValidator")
    public RequestValidationHandler<HeadersAndParams, HoldingDataRequest> holdingDateValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            JwtVerificationService jwtVerificationService
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = getPortfolioHoldingsHeaderAndParamValidators(productIdsRegistry, jwtVerificationService);
        List<Validator<HoldingDataRequest>> requestBodyValidators = List.of(
                new PortfolioSettingsValidator(),
                new StaticDateCountValidator(20),
                new com.morningstar.martapi.validator.portfolioholdings.InvestmentValidator(),
                new com.morningstar.martapi.validator.portfolioholdings.IdTypeValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "holdingDataValidatorSync")
    public RequestValidationHandler<HeadersAndParams, HoldingDataRequest> holdingDataSyncValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            JwtVerificationService jwtVerificationService
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = getPortfolioHoldingsHeaderAndParamValidators(productIdsRegistry, jwtVerificationService);
        List<Validator<HoldingDataRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(HoldingValidationException::new),
                new PortfolioSettingsValidator(),
                new HoldingDateTypeValidator(),
                new StaticDateCountValidator(1),       // for sync
                new com.morningstar.martapi.validator.portfolioholdings.InvestmentValidator(),
                new InvestmentCountValidator(),         // for sync
                new com.morningstar.martapi.validator.portfolioholdings.IdTypeValidator(),
                new HoldingsDataPointsRequestFieldsValidator(),
                new HoldingsDataPointsCountValidator()   // for sync
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "holdingDataValidatorAsync")
    public RequestValidationHandler<HeadersAndParams, HoldingDataRequest> holdingDataASyncValidator(
            @Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry,
            @Qualifier("dataEntitlementService")DataEntitlementService<HoldingDataRequest> dataEntitlementService,
            JwtVerificationService jwtVerificationService
    ) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = getPortfolioHoldingsHeaderAndParamValidators(productIdsRegistry, jwtVerificationService);
        List<Validator<HoldingDataRequest>> requestBodyValidators = List.of(
                new UseCaseValidator<>(HoldingValidationException::new),
                new PortfolioSettingsValidator(),
                new StaticDateCountValidator(20),
                new com.morningstar.martapi.validator.portfolioholdings.InvestmentValidator(),
                new com.morningstar.martapi.validator.portfolioholdings.IdTypeValidator(),
                new HoldingsDataPointsRequestFieldsValidator()
        );
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators, dataEntitlementService);
    }

    private List<Validator<HeadersAndParams>> getPortfolioHoldingsHeaderAndParamValidators(ProductIdsRegistry productIdsRegistry, JwtVerificationService jwtVerificationService) {
        return List.of(
                new AuthTokenValidator(jwtVerificationService),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
    }

    @Bean(name = "clearCacheValidator")
    public RequestValidationHandler<HeadersAndParams, ClearCacheRequest> clearCacheValidator(JwtVerificationService jwtVerificationService) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(jwtVerificationService));
        List<Validator<ClearCacheRequest>> requestBodyValidators = List.of(
                new com.morningstar.martapi.validator.clearcache.InvestmentValidator(),
                new com.morningstar.martapi.validator.clearcache.DataPointValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "licenseAuditValidator")
    public RequestValidationHandler<HeadersAndParams, LicenseAuditEntity> licenseAuditValidator() {
        List<Validator<HeadersAndParams>> headerAndParamValidators = Collections.emptyList();
        List<Validator<LicenseAuditEntity>> requestBodyValidators = List.of(
                new com.morningstar.martapi.validator.licenseapi.LicenseAuditEntityValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }

    @Bean(name = "licenseCellValidator")
    public RequestValidationHandler<HeadersAndParams, LicenseCellEntity> licenseCellValidator(@Qualifier("productIdsRegistry") ProductIdsRegistry productIdsRegistry, JwtVerificationService jwtVerificationService) {
        List<Validator<HeadersAndParams>> headerAndParamValidators = List.of(new AuthTokenValidator(jwtVerificationService), new RequestIdValidator(), new ProductIdValidator(productIdsRegistry));
        List<Validator<LicenseCellEntity>> requestBodyValidators = List.of(
                new com.morningstar.martapi.validator.licenseapi.LicenseCellEntityValidator());
        return new RequestValidationHandler<>(headerAndParamValidators, requestBodyValidators);
    }
}
